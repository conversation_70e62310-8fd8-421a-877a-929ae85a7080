Type: AWS::Glue::Table
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: !Ref GlueDatabaseFraud
  TableInput:
    Name: ${self:custom.TableName}
    TableType: EXTERNAL_TABLE
    Parameters:
      classification: json
      typeOfData: file
      compressionType: none
    StorageDescriptor:
      Columns:
        - Name: paymentdata
          Type: struct<bookingreferenceid:string,bookingdatetime:string,membershipnum:string,pri:string,officeid:string,pos:string,clientapp:string,foptype:string,fop:string,cardtype:string,emailid:string,amount:string,currency:string>
          Comment: Payment data structure
        - Name: events
          Type: struct<`ac-transaction-id`:string,`3DSRequestEvent`:string,`3DSRequestTime`:string,`3DSRequestID`:string,`3DSResponseEvent`:string,`3DSResponseTime`:string,`FraudScreeningRequestTime`:string,`FraudScreeningResponseEvent`:string,`FraudScreeningResponseTime`:string,`FraudScreeningResponseID`:string,`FraudScreeningCaseUpdateEvent`:string,`FraudScreeningCaseUpdateTime`:string,`FraudScreeningCaseUpdateID`:string,`AuthorizationRequestEvent`:string,`AuthorizationRequestTime`:string,`AuthorizationResponseEvent`:string,`AuthorizationResponseTime`:string,`AuthorizationResponseID`:string,`ApplicationEndpoint`:string,`ErrorCode`:string,`ErrorMessage`:string>
          Comment: Events data structure
        - Name: event
          Type: struct<`ac-transaction-id`:string,`3DSRequestEvent`:string,`3DSRequestTime`:string,`3DSRequestID`:string,`3DSResponseEvent`:string,`3DSResponseTime`:string,`FraudScreeningRequestTime`:string,`FraudScreeningResponseEvent`:string,`FraudScreeningResponseTime`:string,`FraudScreeningResponseID`:string,`FraudScreeningCaseUpdateEvent`:string,`FraudScreeningCaseUpdateTime`:string,`FraudScreeningCaseUpdateID`:string,`AuthorizationRequestEvent`:string,`AuthorizationRequestTime`:string,`AuthorizationResponseEvent`:string,`AuthorizationResponseTime`:string,`AuthorizationResponseID`:string,`ApplicationEndpoint`:string,`ErrorCode`:string,`ErrorMessage`:string>
          Comment: Single event data structure (same as events)
        - Name: fraudscreening_data
          Type: string
          Comment: Fraud screening data as JSON string

      Location: s3://${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING}/
      InputFormat: org.apache.hadoop.mapred.TextInputFormat
      OutputFormat: org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat
      SerdeInfo:
        SerializationLibrary: org.openx.data.jsonserde.JsonSerDe
        Parameters:
          # Handle malformed JSON and case sensitivity
          ignore.malformed.json: "true"
          case.insensitive: "false"
          mapping.paymentdata: PaymentData
          mapping.events: Events
          mapping.event: Event
          mapping.fraudscreening_data: FraudScreening_Data
      StoredAsSubDirectories: false
    PartitionKeys:
      - Name: year
        Type: string
      - Name: month
        Type: string
      - Name: day
        Type: string
