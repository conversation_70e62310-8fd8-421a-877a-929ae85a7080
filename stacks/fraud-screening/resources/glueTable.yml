Type: AWS::Glue::Table
Properties:
  CatalogId: !Ref AWS::AccountId
  DatabaseName: !Ref GlueDatabaseFraud
  TableInput:
    Name: ${self:custom.TableName}
    TableType: EXTERNAL_TABLE
    Parameters:
      classification: json
      typeOfData: file
      compressionType: none
    StorageDescriptor:
      Columns:
        - Name: paymentdata
          Type: struct<foptype:string,currency:string,bookingreferenceid:string,pos:string,amount:string,pri:string,fop:string,bookingdatetime:string,cardtype:string,emailid:string,officeid:string,clientapp:string>
          Comment: Payment data structure
        - Name: events
          Type: struct<fraudscreeningresponseevent:string,applicationendpoint:string,ac_transaction_id:string,authorizationrequesttime:string,authorizationrequestevent:string,authorizationresponseevent:string,fraudscreeningresponsetime:string,authorizationresponsetime:string,authorizationresponseid:string,fraudscreeningrequesttime:string,errorcode:string,errormessage:string,fraudscreeningresponseid:string>
          Comment: Events data structure
        - Name: event
          Type: struct<fraudscreeningresponseevent:string,applicationendpoint:string,ac_transaction_id:string,authorizationrequesttime:string,authorizationrequestevent:string,authorizationresponseevent:string,fraudscreeningresponsetime:string,authorizationresponsetime:string,authorizationresponseid:string,fraudscreeningrequesttime:string,errorcode:string,errormessage:string,fraudscreeningresponseid:string>
          Comment: Single event data structure (same as events)
        - Name: fraudscreening_data
          Type: string
          Comment: Fraud screening data as JSON string

      Location: s3://${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_BUCKET}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING_DIR}/${self:custom.defaultParams.${self:provider.stage}.CONNECTOR_CONFIGURATION_TOPIC_FRAUD_SCREENING}/
      InputFormat: org.apache.hadoop.mapred.TextInputFormat
      OutputFormat: org.apache.hadoop.hive.ql.io.HiveIgnoreKeyTextOutputFormat
      SerdeInfo:
        SerializationLibrary: org.openx.data.jsonserde.JsonSerDe
        Parameters:
          ignore.malformed.json: "true"
          case.insensitive: "false"
      StoredAsSubDirectories: false
    PartitionKeys:
      - Name: year
        Type: string
      - Name: month
        Type: string
      - Name: day
        Type: string
