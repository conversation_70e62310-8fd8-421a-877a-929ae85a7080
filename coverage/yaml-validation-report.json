{"issues": [{"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/transportation-voucher/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:35)\n\n 1 | Type: AWS::Glue::Table\n 2 | Properties:\n 3 |     CatalogId: !Ref AWS::AccountId\n---------------------------------------^\n 4 |     DatabaseName: your-database-name\n 5 |     TableInput:", "filePath": "stacks/transportation-voucher/resources/glueTable.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_tv_datastore", "filePath": "stacks/transportation-voucher/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_tv_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseTv\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/transportation-voucher/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/tkt/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_tkt_datastore", "filePath": "stacks/tkt/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:37)\n\n 2 | Properties:\n 3 |   Name: s3_tables_tkt_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 5 |   DatabaseName: !Ref GlueDatabaseTKT\n-----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 7 |   Targets:", "filePath": "stacks/tkt/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (80:48)\n\n 77 |  ... ogDelivery:\n 78 |  ... WatchLogs:\n 79 |  ... bled: true\n 80 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 81 |  ... \n 82 |  ... ", "filePath": "stacks/ssm/serverless.yml", "textRange": {"startLine": 80, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_ssm_datastore", "filePath": "stacks/ssm/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:37)\n\n 2 | Properties:\n 3 |   Name: s3_tables_ssm_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 5 |   DatabaseName: !Ref GlueDatabaseSsm\n-----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 7 |   Targets:", "filePath": "stacks/ssm/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/smartload/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_smartload_datastore", "filePath": "stacks/smartload/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:43)\n\n 2 |  ... \n 3 |  ... tables_smartload_crawler\n 4 |  ... :aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${s ...\n 5 |  ... ame: !Ref GlueDatabaseSmartload\n-----------------------------------------^\n 6 |  ... tion: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\"Ad ...\n 7 |  ... ", "filePath": "stacks/smartload/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/retroclaim/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_retroclaim_datastore", "filePath": "stacks/retroclaim/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:44)\n\n 2 |  ... \n 3 |  ... ables_retroclaim_crawler\n 4 |  ... aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${se ...\n 5 |  ... me: !Ref GlueDatabaseRetroclaim\n-----------------------------------------^\n 6 |  ... ion: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\"Add ...\n 7 |  ... ", "filePath": "stacks/retroclaim/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/retail-partnership/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_retail_p_datastore", "filePath": "stacks/retail-partnership/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:41)\n\n 2 |  ... s:\n 3 |  ... 3_tables_retail_p_crawler\n 4 |  ... rn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.$ ...\n 5 |  ... eName: !Ref GlueDatabaseRetailP\n-----------------------------------------^\n 6 |  ... ration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\" ...\n 7 |  ... :", "filePath": "stacks/retail-partnership/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Sub> (239:129)\n\n 236 |  ... \n 237 |  ... \n 238 |  ... \n 239 |  ... NNECTOR_CONFIGURATION_BUCKET}\"\n------------------------------------------^\n 240 |  ... \n 241 |  ... ", "filePath": "stacks/resources/serverless.yml", "textRange": {"startLine": 239, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/profile/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_profile_datastore", "filePath": "stacks/profile/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:41)\n\n 2 |  ... s:\n 3 |  ... 3_tables_profile_crawler\n 4 |  ... rn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.$ ...\n 5 |  ... eName: !Ref GlueDatabaseProfile\n-----------------------------------------^\n 6 |  ... ration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\" ...\n 7 |  ... :", "filePath": "stacks/profile/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/pool-redemption/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_pool_r_datastore", "filePath": "stacks/pool-redemption/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:39)\n\n 2 |  ... ies:\n 3 |  ...  s3_tables_pool_r_crawler\n 4 |  ...  arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams ...\n 5 |  ... aseName: !Ref GlueDatabasePoolR\n-----------------------------------------^\n 6 |  ... guration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{ ...\n 7 |  ... ts:", "filePath": "stacks/pool-redemption/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/pool-permission/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_pool_p_datastore", "filePath": "stacks/pool-permission/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:39)\n\n 2 |  ... ies:\n 3 |  ...  s3_tables_pool_p_crawler\n 4 |  ...  arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams ...\n 5 |  ... aseName: !Ref GlueDatabasePoolP\n-----------------------------------------^\n 6 |  ... guration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{ ...\n 7 |  ... ts:", "filePath": "stacks/pool-permission/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/pool-balance/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_pool_b_datastore", "filePath": "stacks/pool-balance/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:39)\n\n 2 |  ... ies:\n 3 |  ...  s3_tables_pool_b_crawler\n 4 |  ...  arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams ...\n 5 |  ... aseName: !Ref GlueDatabasePoolB\n-----------------------------------------^\n 6 |  ... guration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{ ...\n 7 |  ... ts:", "filePath": "stacks/pool-balance/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/pool/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_pool_datastore", "filePath": "stacks/pool/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:38)\n\n 2 |  ... ties:\n 3 |  ... : s3_tables_pool_crawler\n 4 |  ... : arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParam ...\n 5 |  ... baseName: !Ref GlueDatabasePool\n-----------------------------------------^\n 6 |  ... iguration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\": ...\n 7 |  ... ets:", "filePath": "stacks/pool/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (80:48)\n\n 77 |  ... ogDelivery:\n 78 |  ... WatchLogs:\n 79 |  ... bled: true\n 80 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 81 |  ... \n 82 |  ... ", "filePath": "stacks/pnr/serverless.yml", "textRange": {"startLine": 80, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_pnr_datastore_v2", "filePath": "stacks/pnr/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:37)\n\n 2 | Properties:\n 3 |   Name: s3_tables_pnr_crawler_v2\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 5 |   DatabaseName: !Ref GlueDatabasePnr\n-----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 7 |   Targets:", "filePath": "stacks/pnr/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/membership-status/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_ms_datastore", "filePath": "stacks/membership-status/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_ms_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseMs\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/membership-status/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/meal-voucher/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_mv_datastore", "filePath": "stacks/meal-voucher/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_mv_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseMv\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/meal-voucher/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (77:48)\n\n 74 |  ... ogDelivery:\n 75 |  ... WatchLogs:\n 76 |  ... bled: true\n 77 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 78 |  ... \n 79 |  ... ", "filePath": "stacks/jct-roster-output/serverless.yml", "textRange": {"startLine": 77, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_jct_roster_output", "filePath": "stacks/jct-roster-output/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:43)\n\n 2 |  ... \n 3 |  ... tables_jct_roster_output_crawler\n 4 |  ... :aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${s ...\n 5 |  ... ame: !Ref GlueDatabaseJctRoster\n-----------------------------------------^\n 6 |  ... tion: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\"Ad ...\n 7 |  ... ", "filePath": "stacks/jct-roster-output/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/ifly-total-bal/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_total_bal_datastore", "filePath": "stacks/ifly-total-bal/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:42)\n\n 2 |  ... :\n 3 |  ... _tables_total_bal_crawler\n 4 |  ... n:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${ ...\n 5 |  ... Name: !Ref GlueDatabaseTotalBal\n-----------------------------------------^\n 6 |  ... ation: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\"A ...\n 7 |  ... ", "filePath": "stacks/ifly-total-bal/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/ifly-tierstatus/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_ifly_tierstatus_datastore", "filePath": "stacks/ifly-tierstatus/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:48)\n\n 2 |  ... \n 3 |  ... s_ifly_tierstatus_crawler\n 4 |  ... iam::${aws:accountId}:role/${self:custom.defaultParams.${self:p ...\n 5 |  ... !Ref GlueDatabaseIflyTierstatus\n-----------------------------------------^\n 6 |  ...  \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\"AddOrUp ...\n 7 |  ... ", "filePath": "stacks/ifly-tierstatus/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/hotel/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:35)\n\n 1 | Type: AWS::Glue::Table\n 2 | Properties:\n 3 |     CatalogId: !Ref AWS::AccountId\n---------------------------------------^\n 4 |     DatabaseName: your-database-name\n 5 |     TableInput:", "filePath": "stacks/hotel/resources/glueTable.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_hc_datastore", "filePath": "stacks/hotel/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_hc_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseHc\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/hotel/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (80:48)\n\n 77 |  ... ogDelivery:\n 78 |  ... WatchLogs:\n 79 |  ... bled: true\n 80 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 81 |  ... \n 82 |  ... ", "filePath": "stacks/fsum/serverless.yml", "textRange": {"startLine": 80, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_fsum_datastore_v2", "filePath": "stacks/fsum/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:38)\n\n 2 |  ... ties:\n 3 |  ... : s3_tables_fsum_crawler\n 4 |  ... : arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParam ...\n 5 |  ... baseName: !Ref GlueDatabaseFsum\n-----------------------------------------^\n 6 |  ... iguration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\": ...\n 7 |  ... ets:", "filePath": "stacks/fsum/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/frozen-stage/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_fs_datastore", "filePath": "stacks/frozen-stage/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_fs_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseFs\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/frozen-stage/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (140:48)\n\n 137 |  ... ogDelivery:\n 138 |  ... WatchLogs:\n 139 |  ... bled: true\n 140 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n------------------------------------------^\n 141 |  ... \n 142 |  ... ", "filePath": "stacks/fraud-screening/serverless.yml", "textRange": {"startLine": 140, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Table\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseName: !Ref GlueDatabaseFraud\n 5 |   TableInput:", "filePath": "stacks/fraud-screening/resources/glueTable.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: ac-odh-event-stream-db-fraud-screening", "filePath": "stacks/fraud-screening/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:39)\n\n 2 |  ... ies:\n 3 |  ...  s3_tables_fraud_crawler\n 4 |  ...  arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams ...\n 5 |  ... aseName: !Ref GlueDatabaseFraud\n-----------------------------------------^\n 6 |  ... guration: |\n 7 |  ... ", "filePath": "stacks/fraud-screening/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (7:33)\n\n 4 |   - GlueDatabaseFraud\n 5 |   - GlueTableFraud\n 6 | Properties:\n 7 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 8 |   DatabaseName: ${self:custom.DatabaseName}\n 9 |   TableInput:", "filePath": "stacks/fraud-screening/resources/fraudScreeningView.yml", "textRange": {"startLine": 7, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (58:56)\n\n 55 |  ... \n 56 |  ... \n 57 |  ... \n 58 |  ... logGroupFlightEventFlightAware\n-----------------------------------------^\n 59 |  ... \n 60 |  ... ", "filePath": "stacks/flight-tracking/resources/weather-event-flightaware/mskConnect.yml", "textRange": {"startLine": 58, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (7:47)\n\n 4 |  ... \n 5 |  ... es_flight_tracker_crawler_weather\n 6 |  ... :iam::${aws:accountId}:role/${self:custom.defaultParams.${self: ...\n 7 |  ...  !Ref glueDatabaseFlightTracker\n-----------------------------------------^\n 8 |  ... : \"{\\\"Version\\\":1.0,\\\"Grouping\\\":{\\\"TableGroupingPolicy\\\":\\\"Com ...\n 9 |  ... ", "filePath": "stacks/flight-tracking/resources/weather-event-flightaware/glueCrawler.yml", "textRange": {"startLine": 7, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (58:56)\n\n 55 |  ... \n 56 |  ... \n 57 |  ... \n 58 |  ... logGroupFlightEventFlightAware\n-----------------------------------------^\n 59 |  ... \n 60 |  ... ", "filePath": "stacks/flight-tracking/resources/flight-event-flightaware/mskConnect.yml", "textRange": {"startLine": 58, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (7:47)\n\n 4 |  ... \n 5 |  ... es_flight_tracker_crawler\n 6 |  ... :iam::${aws:accountId}:role/${self:custom.defaultParams.${self: ...\n 7 |  ...  !Ref glueDatabaseFlightTracker\n-----------------------------------------^\n 8 |  ... : \"{\\\"Version\\\":1.0,\\\"Grouping\\\":{\\\"TableGroupingPolicy\\\":\\\"Com ...\n 9 |  ... ", "filePath": "stacks/flight-tracking/resources/flight-event-flightaware/glueCrawler.yml", "textRange": {"startLine": 7, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (58:56)\n\n 55 |  ... \n 56 |  ... \n 57 |  ... \n 58 |  ... logGroupFlightEventFlightAware\n-----------------------------------------^\n 59 |  ... \n 60 |  ... ", "filePath": "stacks/flight-tracking/resources/flight-event-ar24/mskConnect.yml", "textRange": {"startLine": 58, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (7:47)\n\n 4 |  ... \n 5 |  ... es_flight_tracker_crawler_ar24\n 6 |  ... :iam::${aws:accountId}:role/${self:custom.defaultParams.${self: ...\n 7 |  ...  !Ref glueDatabaseFlightTracker\n-----------------------------------------^\n 8 |  ... : \"{\\\"Version\\\":1.0,\\\"Grouping\\\":{\\\"TableGroupingPolicy\\\":\\\"Com ...\n 9 |  ... ", "filePath": "stacks/flight-tracking/resources/flight-event-ar24/glueCrawler.yml", "textRange": {"startLine": 7, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_flight_tracker_datastore", "filePath": "stacks/flight-tracking/resources/common/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (85:48)\n\n 82 |  ... ogDelivery:\n 83 |  ... WatchLogs:\n 84 |  ... bled: true\n 85 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 86 |  ... \n 87 |  ... ", "filePath": "stacks/fdm/serverless.yml", "textRange": {"startLine": 85, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_fdm_datastore_v2", "filePath": "stacks/fdm/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:37)\n\n 2 | Properties:\n 3 |   Name: s3_tables_fdm_crawler_v2\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 5 |   DatabaseName: !Ref GlueDatabaseFdm\n-----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 7 |   Targets:", "filePath": "stacks/fdm/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/csg/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_csg_datastore_v2", "filePath": "stacks/csg/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:37)\n\n 2 | Properties:\n 3 |   Name: s3_tables_csg_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 5 |   DatabaseName: !Ref GlueDatabaseCsg\n-----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 7 |   Targets:", "filePath": "stacks/csg/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/cp-cl1b/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_cpcl1b_datastore", "filePath": "stacks/cp-cl1b/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_cpcl1b_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseCpcl1b\n-----------------------------------------^\n 6 |  ... uration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\ ...\n 7 |  ... s:", "filePath": "stacks/cp-cl1b/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/cp-cl1a/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_cpcl1a_datastore", "filePath": "stacks/cp-cl1a/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_cpcl1a_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseCpcl1a\n-----------------------------------------^\n 6 |  ... uration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\ ...\n 7 |  ... s:", "filePath": "stacks/cp-cl1a/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/compensation/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:35)\n\n 1 | Type: AWS::Glue::Table\n 2 | Properties:\n 3 |     CatalogId: !Ref AWS::AccountId\n---------------------------------------^\n 4 |     DatabaseName: your-database-name\n 5 |     TableInput:", "filePath": "stacks/compensation/resources/glueTable.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_ca_datastore", "filePath": "stacks/compensation/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_ca_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseCa\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/compensation/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/cobrand-card/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_cb_card_datastore", "filePath": "stacks/cobrand-card/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_cb_card_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseCbCard\n-----------------------------------------^\n 6 |  ... uration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\ ...\n 7 |  ... s:", "filePath": "stacks/cobrand-card/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (84:48)\n\n 81 |  ... ogDelivery:\n 82 |  ... WatchLogs:\n 83 |  ... bled: true\n 84 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 85 |  ... \n 86 |  ... ", "filePath": "stacks/cm-feed/serverless.yml", "textRange": {"startLine": 84, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Table\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseName: !Ref GlueDatabaseCmFeed\n 5 |   TableInput:", "filePath": "stacks/cm-feed/resources/glueTable.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_cm_feed_datastore_v1", "filePath": "stacks/cm-feed/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_cm_feed_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseCmFeed\n-----------------------------------------^\n 6 |  ... uration: >\n 7 |  ... ", "filePath": "stacks/cm-feed/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/certificate-status/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_cf_status_datastore", "filePath": "stacks/certificate-status/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:42)\n\n 2 |  ... :\n 3 |  ... _tables_cf_status_crawler\n 4 |  ... n:aws:iam::${aws:accountId}:role/${self:custom.defaultParams.${ ...\n 5 |  ... Name: !Ref GlueDatabaseCfStatus\n-----------------------------------------^\n 6 |  ... ation: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\\"A ...\n 7 |  ... ", "filePath": "stacks/certificate-status/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/ccm-ctr/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_ccm_ctr_datastore", "filePath": "stacks/ccm-ctr/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_ccm_ctr_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseCcmCtr\n-----------------------------------------^\n 6 |  ... uration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\ ...\n 7 |  ... s:", "filePath": "stacks/ccm-ctr/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/ccm-aes/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_ccm_aes_datastore", "filePath": "stacks/ccm-aes/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_ccm_aes_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseCcmAes\n-----------------------------------------^\n 6 |  ... uration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\ ...\n 7 |  ... s:", "filePath": "stacks/ccm-aes/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (80:48)\n\n 77 |  ... ogDelivery:\n 78 |  ... WatchLogs:\n 79 |  ... bled: true\n 80 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 81 |  ... \n 82 |  ... ", "filePath": "stacks/alg/serverless.yml", "textRange": {"startLine": 80, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_alg_datastore_v2", "filePath": "stacks/alg/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:37)\n\n 2 | Properties:\n 3 |   Name: s3_tables_alg_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 5 |   DatabaseName: !Ref GlueDatabaseAlg\n-----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 7 |   Targets:", "filePath": "stacks/alg/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (81:48)\n\n 78 |  ... ogDelivery:\n 79 |  ... WatchLogs:\n 80 |  ... bled: true\n 81 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 82 |  ... \n 83 |  ... ", "filePath": "stacks/aflvfl/serverless.yml", "textRange": {"startLine": 81, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_aflvfl_datastore_v2", "filePath": "stacks/aflvfl/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:40)\n\n 2 |  ... es:\n 3 |  ... s3_tables_aflvfl_crawler\n 4 |  ... arn:aws:iam::${aws:accountId}:role/${self:custom.defaultParams. ...\n 5 |  ... seName: !Ref GlueDatabaseAflvfl\n-----------------------------------------^\n 6 |  ... uration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\":{\\ ...\n 7 |  ... s:", "filePath": "stacks/aflvfl/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/adm/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: adm_s3_glue_database_v3", "filePath": "stacks/adm/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (6:37)\n\n 3 | Properties:\n 4 |   Name: s3_crawler_adm_v3\n 5 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPara ...\n 6 |   DatabaseName: !Ref GlueDatabaseAdm\n-----------------------------------------^\n 7 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\\" ...\n 8 |   Targets:", "filePath": "stacks/adm/resources/glueCrawler.yml", "textRange": {"startLine": 6, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (82:48)\n\n 79 |  ... ogDelivery:\n 80 |  ... WatchLogs:\n 81 |  ... bled: true\n 82 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 83 |  ... \n 84 |  ... ", "filePath": "stacks/account-status/serverless.yml", "textRange": {"startLine": 82, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_as_datastore", "filePath": "stacks/account-status/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_as_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseAs\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/account-status/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (83:48)\n\n 80 |  ... ogDelivery:\n 81 |  ... WatchLogs:\n 82 |  ... bled: true\n 83 |  ... Group: !Ref <PERSON><PERSON>kConnectLog\n-----------------------------------------^\n 84 |  ... \n 85 |  ... ", "filePath": "stacks/account-merge/serverless.yml", "textRange": {"startLine": 83, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (3:33)\n\n 1 | Type: AWS::Glue::Database\n 2 | Properties:\n 3 |   CatalogId: !Ref AWS::AccountId\n-------------------------------------^\n 4 |   DatabaseInput:\n 5 |     Name: s3_am_datastore", "filePath": "stacks/account-merge/resources/glueDatabase.yml", "textRange": {"startLine": 3, "startColumn": 1}}}, {"engineId": "yaml-validator", "ruleId": "SYNTAX_ERROR", "severity": "BLOCKER", "type": "CODE_SMELL", "primaryLocation": {"message": "unknown tag !<!Ref> (5:36)\n\n 2 | Properties:\n 3 |   Name: s3_tables_am_crawler\n 4 |   Role: arn:aws:iam::${aws:accountId}:role/${self:custom.defaultPar ...\n 5 |   DatabaseName: !Ref GlueDatabaseAm\n----------------------------------------^\n 6 |   Configuration: \"{\\\"Version\\\":1.0,\\\"CrawlerOutput\\\":{\\\"Partitions\\ ...\n 7 |   Targets:", "filePath": "stacks/account-merge/resources/glueCrawler.yml", "textRange": {"startLine": 5, "startColumn": 1}}}]}