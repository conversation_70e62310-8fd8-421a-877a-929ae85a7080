# Source and test configuration
sonar.sources=stacks,defaults.yml
sonar.tests=tests
sonar.exclusions=images/**,node_modules/**,coverage/**,.git/**,*.md
sonar.test.inclusions=tests/**/*.test.js,tests/**/*.spec.js

# Language-specific settings
sonar.javascript.lcov.reportPaths=coverage/lcov.info
sonar.testExecutionReportPaths=coverage/reporter.xml

# YAML file analysis
sonar.yaml.file.suffixes=.yml,.yaml
sonar.yaml.schemas=cloudformation:stacks/**/*.yml

# CloudFormation and Infrastructure analysis
sonar.cfn.file.suffixes=.yml,.yaml,.json
sonar.cfn.schemas=stacks/**/*.yml,defaults.yml

# Quality gate and coverage
sonar.coverage.exclusions=tests/**/*,**/*.test.js,**/*.spec.js
sonar.cpd.exclusions=tests/**/*

# Additional file patterns for infrastructure
sonar.inclusions=**/*.yml,**/*.yaml,**/*.js,**/*.json